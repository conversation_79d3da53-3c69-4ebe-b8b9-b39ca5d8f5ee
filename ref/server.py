#!/usr/bin/env python3
#
# MCP Server for providing internal context using fastmcp
#

import json
import os
import logging
import re
from fastmcp import FastMCP, Context
from starlette.requests import Request
from starlette.responses import Response
from fastmcp.exceptions import ToolError
from pydantic import BaseModel, Field
from typing import List, Annotated
import io
import csv
import pandas as pd
import fitz  # PyMuPDF
import pytesseract
from PIL import Image

# Get the absolute path of the script's directory
# This is crucial for correctly locating `resources.json`
# when the server is run from a different working directory.
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
RESOURCE_INDEX_PATH = os.path.join(SCRIPT_DIR, 'resources.json')

def setup_logging():
    """
    Sets up logging to both console and file.
    """
    log_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # File handler
    file_handler = logging.FileHandler('mcp_conversation.log')
    file_handler.setFormatter(log_formatter)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(log_formatter)
    
    # Get root logger
    root_logger = logging.getLogger()
    if not root_logger.hasHandlers():
        root_logger.setLevel(logging.INFO)
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)

# Call the setup function
setup_logging()

mcp_app = FastMCP(
    name="atc-assist-mcp",
    instructions=(
        "A server providing internal context including ATC (Apple USB TypeC) related documents(terminolgy, spec, etc.), ATC test priority device list, and other internal resources(platform config, internal code name).\n\n"
        "Usage Guide:\n"
        "1. Discover available resources:\n"
        "   - Use the `show_doc_index` tool to get a list of all available doc IDs and the document descriptions.\n"
        "   - `use_mcp_tool('atc-assist-mcp', 'show_doc_index')`\n\n"
        "2. Retrieve doc content:\n"
        "   - Use the `get_doc` tool with one or more resource IDs to fetch their content.\n\n"
        "3. Filter CSV resources:\n"
        "   - Use the `get_filtered_list` tool to search and filter contents from CSV files."
    ),
    mask_error_details=True,
)
app = mcp_app.http_app
_RESOURCES_CACHE = None
_CHIPSET_MAPPING_CACHE = None

def load_chipset_brand_mapping():
    """
    Parses docs/Chipset_Brand_Mapping.md to create a brand-to-convention mapping.
    """
    global _CHIPSET_MAPPING_CACHE
    if _CHIPSET_MAPPING_CACHE is not None:
        return _CHIPSET_MAPPING_CACHE

    mapping = {}
    try:
        mapping_file_path = os.path.join(SCRIPT_DIR, 'docs', 'Chipset_Brand_Mapping.md')
        with open(mapping_file_path, 'r') as f:
            lines = f.readlines()
        
        brand = None
        for line in lines:
            line = line.strip()
            if line.startswith('### '):
                brand = line.replace('###', '').strip()
            elif brand and line.startswith('- **Convention:**'):
                conventions = re.findall(r'`([^`]+)`', line)
                # Filter out descriptive parts of the convention string
                conventions = [c for c in conventions if ' ' not in c or c.endswith('-')]
                if brand and conventions:
                    mapping[brand.lower()] = conventions
                brand = None
    except Exception as e:
        logging.error(f"Error loading chipset brand mapping: {e}")
    
    _CHIPSET_MAPPING_CACHE = mapping
    return _CHIPSET_MAPPING_CACHE

def load_resources():
    """
    Loads resources from resources.json, caching the result.
    """
    global _RESOURCES_CACHE
    if _RESOURCES_CACHE is not None:
        return _RESOURCES_CACHE

    all_resources = []
    if os.path.exists(RESOURCE_INDEX_PATH):
        with open(RESOURCE_INDEX_PATH, 'r') as f:
            all_resources = json.load(f)

    for resource in all_resources:
        if resource.get('path', '').lower().endswith('.csv') and resource.get('schema'):
            try:
                csv_path = os.path.join(SCRIPT_DIR, resource['path'])
                if os.path.exists(csv_path):
                    with open(csv_path, 'r', encoding='utf-8') as csv_file:
                        reader = csv.reader(csv_file)
                        next(reader, None)  # Skip header
                        first_row = next(reader, None)
                        
                        if first_row:
                            example_str = "\n- `Example`: "
                            example_pairs = []
                            schema = resource.get('schema', [])
                            for i, value in enumerate(first_row):
                                if i < len(schema) and value:
                                    example_pairs.append(f"'{schema[i]}': '{value}'")
                            
                            example_str += ", ".join(example_pairs)
                            resource['description'] += example_str
            except Exception as e:
                logging.warning(f"Could not generate example for {resource.get('path')}: {e}")

    _RESOURCES_CACHE = all_resources
    return _RESOURCES_CACHE


from fastmcp import FastMCP, Context

@mcp_app.tool()
async def show_doc_index(ctx: Context) -> str:
    """
    Shows the document index with description.

    This tool can be used to discover available documents by returning the entire doc index inculding the doc id and the description. So you can choose which should be used to answer questions.
    """
    logging.info("Executing show_doc_index")
    await ctx.info("Returning all docs.")
    resources = load_resources()
    response_data = {
        "index": resources,
        "prompt": "According above index, identify which specific documents are most likely to contain the information needed to answer the question. Then use get_doc tool to retreive these documents. If you can't find any related resources tell user the lindex and ask user to pick"
    }
    response = json.dumps(response_data, indent=2)
    logging.info(f"Response from show_doc_index")
    logging.debug(f"Response from show_doc_index: {response}")
    return response


@mcp_app.tool()
async def get_doc(
    ctx: Context,
    resource_id: Annotated[List[int], Field(description="A list of resource IDs to retrieve.", max_length=5)]
) -> str:
    """
    Gets the content of one or more resources.

    This tool retrieves the content of resources given a list of their IDs.
    If you do not know the resource ID, use the `show_resource_index` tool first to see all available resources. And then choose the 2 most relevant resources you need according to the description.

    - For text-based files, it returns the string content.
    - For csv files, it returns the string content.

    When you using these documents answer questions, please parse the csv table into a table with the format you support.
    """
    
    logging.debug(f"Entering get_doc with resource_id: {resource_id}")
    await ctx.info(f"Getting resources with ids: {resource_id}")
    resources = load_resources()
    logging.debug(f"Loaded resources: {resources}")
    all_content = []
    total_resources = len(resource_id)

    for i, r_id in enumerate(resource_id):
        logging.debug(f"Processing resource ID: {r_id}")
        await ctx.report_progress(progress=i, total=total_resources)
        resource_info = next((res for res in resources if res.get("id") == r_id), None)
        logging.debug(f"Found resource info: {resource_info}")

        content = ""
        if not resource_info:
            content = f"Resource with id '{r_id}' not found."
            logging.warning(content)
            all_content.append({"id": r_id, "content": content})
            continue

        resource_path = resource_info['path']
        local_path = os.path.join(SCRIPT_DIR, resource_path)
        logging.debug(f"Local path for resource: {local_path}")

        file_content = None
        if os.path.exists(local_path):
            with open(local_path, 'rb') as f:
                file_content = f.read()
            logging.debug(f"Successfully read file content for {local_path}")
        else:
            content = f"Local resource file not found at path: {local_path}"
            logging.error(content)
            all_content.append({"id": r_id, "content": content})
            continue

        if file_content is None:
            content = f"Could not retrieve resource file at path: {resource_path}"
            logging.error(content)
            all_content.append({"id": r_id, "content": content})
            continue

        try:
            logging.debug(f"Processing file type for: {resource_path}")
            if resource_path.lower().endswith('.pdf'):
                logging.debug("Detected PDF file. Starting PDF processing.")
                doc = fitz.open(stream=io.BytesIO(file_content), filetype="pdf")
                full_text = ""
                for page_num in range(len(doc)):
                    logging.debug(f"Processing page {page_num + 1}/{len(doc)}")
                    page = doc.load_page(page_num)
                    full_text += page.get_text()
                    tables = page.find_tables()
                    if tables:
                        logging.debug(f"Found {len(tables)} tables on page {page_num + 1}")
                        full_text += "\n\n--- Extracted Tables ---\n"
                        for j, table in enumerate(tables):
                            full_text += f"\n--- Table {j+1} on Page {page_num+1} ---\n"
                            table_data = table.extract()
                            if table_data:
                                for row in table_data:
                                    cleaned_row = [str(cell).replace('\n', ' ') if cell is not None else "" for cell in row]
                                    full_text += ",".join(f'"{item}"' for item in cleaned_row) + "\n"
                            else:
                                full_text += "No data found in this table.\n"
                    image_list = page.get_images(full=True)
                    if image_list:
                        logging.debug(f"Found {len(image_list)} images on page {page_num + 1}")
                        full_text += "\n\n--- OCR Results from Charts/Images ---\n"
                    for img_index, img in enumerate(image_list):
                        logging.debug(f"Processing image {img_index + 1} on page {page_num + 1}")
                        xref = img[0]
                        base_image = doc.extract_image(xref)
                        image_bytes = base_image["image"]
                        image = Image.open(io.BytesIO(image_bytes))
                        ocr_text = pytesseract.image_to_string(image)
                        full_text += f"\n--- Image {img_index+1} on Page {page_num+1} ---\n"
                        full_text += ocr_text
                content = full_text
                logging.debug("Finished PDF processing.")
            elif resource_path.lower().endswith(('.md', '.txt', '.csv')):
                logging.debug("Detected text-based file (md, txt, csv). Decoding as UTF-8.")
                content = file_content.decode('utf-8')
            else:
                logging.debug("Attempting to decode as text, will fall back to binary warning.")
                try:
                    content = file_content.decode('utf-8')
                except UnicodeDecodeError:
                    logging.warning(f"Could not decode {resource_path} as UTF-8. Marking as binary.")
                    content = "Binary file content cannot be displayed as text."
            all_content.append({"id": r_id, "content": content})
        except Exception as e:
            error_message = f"Error processing resource '{r_id}': {e}"
            logging.error(error_message, exc_info=True)
            await ctx.error(error_message)
            content = f"Failed to process resource '{r_id}'. Please check the resource format and try again."
            all_content.append({"id": r_id, "content": content})
    await ctx.report_progress(progress=total_resources, total=total_resources)
    response = json.dumps(all_content, indent=2)
    logging.debug(f"Response from get_doc: {response}")
    return response


class FilterGroup(BaseModel):
    resource_id: int = Field(description="The resource ID of the CSV file to filter.")
    filter: str = Field(description="The string to filter the CSV by.")
    column_name: str = Field(description="The name of the column to filter on.")

@mcp_app.tool()
async def get_filtered_list(
    ctx: Context,
    filter_groups: Annotated[List[FilterGroup], Field(description="A list of filter groups to apply.")],
    operator: Annotated[str, Field(description="The logical operator to combine filter groups ('and' or 'or').")] = "or"
) -> str:
    """
    Filters multiple CSV resources based on a list of filter groups and returns the matching rows and a combined count.

    :param filter_groups: A list of filter group objects. Each object must have the following keys:
                          - `resource_id` (int): The resource ID of the CSV file to filter.
                          - `filter` (str): The string to filter the CSV by (case-insensitive).
                          - `column_name` (str): The name of the column to filter on.
    :param operator: The logical operator to combine the results of the filter groups. Can be "and" or "or". Defaults to "or".
    :return: A JSON string with the following keys:
             - `total_rows` (int): The total number of rows across all queried CSV files before filtering.
             - `total_count` (int): The total number of matching rows from all specified resources.
             - `results` (List[List[str]]): The combined list of matching rows, including the header.

    **Example 1: Find all "USB P1" devices in the Priority Device List.**
    ```json
    {
      "filter_groups": [
        {
          "resource_id": 100002,
          "filter": "USB P1",
          "column_name": "Priority"
        }
      ]
    }
    ```

    **Example 2: Find all devices that are either "USB P0" OR "CIO P0" in the Priority Device List.**
    ```json
    {
      "filter_groups": [
        {
          "resource_id": 100002,
          "filter": "USB P0",
          "column_name": "Priority"
        },
        {
          "resource_id": 100002,
          "filter": "CIO P0",
          "column_name": "Priority"
        }
      ],
      "operator": "or"
    }
    ```

    **Example 3: Find all Mac products that have "Ace3 A4" as the "PD controller" AND "RT15 B1" as the "Retimer".**
    ```json
    {
      "filter_groups": [
        {
          "resource_id": 100003,
          "filter": "Ace3 A4",
          "column_name": "PD controller"
        },
        {
          "resource_id": 100003,
          "filter": "RT15 B1",
          "column_name": "Retimer"
        }
      ],
      "operator": "and"
    }
    ```

    If the column_name='chipsets', please read Chipset_Brand_Mapping first.
    """
    logging.info(f"Executing get_filtered_list with {len(filter_groups)} filter groups and operator '{operator}'.")
    await ctx.info(f"Processing {len(filter_groups)} filter groups with operator '{operator}'.")

    if operator.lower() not in ['and', 'or']:
        raise ToolError("Invalid operator. Must be 'and' or 'or'.")

    resources = load_resources()
    chipset_mapping = load_chipset_brand_mapping()
    
    # Group filters by resource_id
    filters_by_resource = {}
    for group in filter_groups:
        if group.resource_id not in filters_by_resource:
            filters_by_resource[group.resource_id] = []
        filters_by_resource[group.resource_id].append(group)

    all_matching_dfs = []
    total_rows_all_files = 0

    for resource_id, groups in filters_by_resource.items():
        resource_info = next((res for res in resources if res.get("id") == resource_id), None)

        if not resource_info:
            logging.warning(f"Resource with id '{resource_id}' not found.")
            continue

        resource_path = resource_info['path']
        if not resource_path.lower().endswith('.csv'):
            logging.warning(f"Resource with id '{resource_id}' is not a CSV file.")
            continue

        local_path = os.path.join(SCRIPT_DIR, resource_path)
        if not os.path.exists(local_path):
            logging.warning(f"Local resource file not found at path: {local_path}")
            continue

        try:
            df = pd.read_csv(local_path)
            total_rows_all_files += len(df)
            
            # Store conditions for the current resource_id
            conditions = []
            for group in groups:
                if group.column_name not in df.columns:
                    logging.warning(f"Column '{group.column_name}' not found in resource {resource_id}.")
                    continue
                
                # Ensure the column is of string type for .str accessor
                df[group.column_name] = df[group.column_name].astype(str)
                
                is_chipset_col = group.column_name.lower() in ['chipset', 'chipsets', 'pd controller', 'retimer', 'usb hub']
                filter_val_lower = group.filter.lower()

                if is_chipset_col and filter_val_lower in chipset_mapping:
                    conventions = chipset_mapping[filter_val_lower]
                    chipset_conditions = []
                    for conv in conventions:
                        chipset_conditions.append(df[group.column_name].str.contains(conv, case=False, na=False))
                    
                    if chipset_conditions:
                        # Combine with OR
                        combined_chipset_condition = pd.Series(False, index=df.index)
                        for cond in chipset_conditions:
                            combined_chipset_condition |= cond
                        conditions.append(combined_chipset_condition)
                else:
                    # Case-insensitive "contains" search
                    condition = df[group.column_name].str.contains(group.filter, case=False, na=False)
                    conditions.append(condition)

            if not conditions:
                continue

            # Combine conditions for the current resource_id
            if operator.lower() == 'or':
                combined_condition = pd.Series(False, index=df.index)
                for condition in conditions:
                    combined_condition |= condition
            else:  # 'and'
                combined_condition = pd.Series(True, index=df.index)
                for condition in conditions:
                    combined_condition &= condition
            
            matching_df = df[combined_condition]
            if not matching_df.empty:
                all_matching_dfs.append(matching_df)

        except Exception as e:
            logging.error(f"Error processing CSV file {local_path} with pandas: {e}", exc_info=True)
            continue

    if not all_matching_dfs:
        logging.info(f"Found 0 matching rows for filter groups: {filter_groups}")
        return "The previous attempt yielded no result with current filter. Now You should use get_doc tool to view the doc then combining your knowledge to refine your search criteria. Re-try get_filtered_list tool with new filter. NOTE: Do not answer based on the content, only use it to refine your search criteria."

    # Combine all resulting dataframes
    final_df = pd.concat(all_matching_dfs).drop_duplicates().reset_index(drop=True)
    
    final_header = final_df.columns.tolist()
    final_rows = final_df.values.tolist()

    response_data = {
        "total_rows": total_rows_all_files,
        "total_count": len(final_rows),
        "results": [final_header] + final_rows,
        "prompt": "Radar links for markdown render like this: This is a test radar link: [rdar://resdef/25128](https://rdar.apple.com/resdef/25128)"
    }
    response = json.dumps(response_data, indent=2)
    logging.info(f"Found a total of {len(final_rows)} matching rows for filter groups: {filter_groups}")
    return response


if __name__ == "__main__":
    import argparse
    import sys
    # Separate script arguments from uvicorn arguments
    parser = argparse.ArgumentParser(description="Run the MCP server in STDIO or HTTP mode.")
    parser.add_argument('--http', action='store_true', help="Run in HTTP mode (default is STDIO).")
    
    # Parse known args, allow others to be passed to uvicorn
    args, unknown = parser.parse_known_args()

    if args.http:
        import uvicorn
        
        # Custom logging configuration for uvicorn
        LOGGING_CONFIG = {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "()": "uvicorn.logging.DefaultFormatter",
                    "fmt": "%(levelprefix)s %(message)s",
                    "use_colors": None,
                },
                "access": {
                    "()": "uvicorn.logging.AccessFormatter",
                    "fmt": '%(asctime)s - %(levelprefix)s %(client_addr)s - "%(request_line)s" %(status_code)s',
                    "datefmt": "%Y-%m-%d %H:%M:%S",
                },
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stderr",
                },
                "access": {
                    "formatter": "access",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stdout",
                },
            },
            "loggers": {
                "uvicorn": {
                    "handlers": ["default"],
                    "level": "INFO",
                    "propagate": False,
                },
                "uvicorn.error": {
                    "level": "INFO",
                },
                "uvicorn.access": {
                    "handlers": ["access"],
                    "level": "INFO",
                    "propagate": False,
                },
            },
        }

        # Parse uvicorn arguments from unknown args
        host = '0.0.0.0'
        port = 8000
        reload = '--reload' in unknown
        
        try:
            if '--host' in unknown:
                host = unknown[unknown.index('--host') + 1]
            if '--port' in unknown:
                port = int(unknown[unknown.index('--port') + 1])
        except (ValueError, IndexError):
            print("Warning: Could not parse --host or --port arguments for uvicorn.")
            pass

        # To resolve the "ASGI app factory detected" warning, we use uvicorn.run
        # and explicitly pass the app string and set factory=True.
        # This is a cleaner way to run uvicorn programmatically.
        uvicorn.run("server:app", host=host, port=port, factory=True, reload=reload, log_config=LOGGING_CONFIG)
    else:
        mcp_app.run()